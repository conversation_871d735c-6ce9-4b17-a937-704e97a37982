cmake_minimum_required(VERSION 3.10)
project(TireStitcher)

# Force MinGW compiler
set(CMAKE_C_COMPILER "gcc")
set(CMAKE_CXX_COMPILER "g++")

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directory to ReifenScanner folder (parent of TireStitcher)
# Use absolute path to avoid any confusion
get_filename_component(REIFEN_SCANNER_DIR "${CMAKE_CURRENT_SOURCE_DIR}/.." ABSOLUTE)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${REIFEN_SCANNER_DIR})

# Find OpenMP for parallel processing
# MinGW/GCC OpenMP configuration
find_package(OpenMP)
if(OpenMP_CXX_FOUND)
    message(STATUS "OpenMP found, enabling parallel processing")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
    # Note: _OPENMP is automatically defined by the compiler when using -fopenmp
    # No need to manually add -D_OPENMP to avoid redefinition warnings
else()
    message(WARNING "OpenMP not found, parallel processing will be disabled")
endif()

# Optimizations for Release mode
# MinGW/GCC optimization flags
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")

# Find OpenCV
# Comment out the old method of setting OpenCV_DIR
# set(OpenCV_DIR "A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/lib/cmake/opencv4" CACHE PATH "Path to OpenCV MinGW build")

# Add the user-specified OpenCV root directory to CMAKE_PREFIX_PATH
# This helps CMake find the OpenCV installation
# list(APPEND CMAKE_PREFIX_PATH "A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64") # Commented out hardcoded path

# Add compiler definition for debug logging
add_definitions(-DDEBUG_PATH_LOGGING)

# Add additional compiler flags for MinGW compatibility
if(MINGW)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
    # Disable some warnings that might appear with MinGW
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-deprecated-declarations -Wno-unused-variable")
endif()
find_package(OpenCV REQUIRED)

if(OpenCV_FOUND)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
    message(STATUS "OpenCV include path: ${OpenCV_INCLUDE_DIRS}")
else()
    message(FATAL_ERROR "OpenCV not found. Please install OpenCV and set OpenCV_DIR environment variable.")
endif()

# Include OpenCV headers
include_directories(${OpenCV_INCLUDE_DIRS})

# Define source files for stitch_tire_4k (4K pipeline only)
set(STITCH_TIRE_4K_SOURCES
    main.cpp
    stitching_4k/utils.cpp
    stitching_4k/image_loader.cpp
    stitching_4k/strip_extraction.cpp
    stitching_4k/movement_detection.cpp
    stitching_4k/image_blending.cpp
    stitching_4k/panorama_processor.cpp
    stitching_4k/fs_util.cpp
    stitching_4k/frame_extraction_utils.cpp
    stitching_4k/resolution_config.cpp
)

# Define source files for stitch_tire_8k (8K pipeline only)
set(STITCH_TIRE_8K_SOURCES
    main.cpp
    stitching_8k/utils.cpp
    stitching_8k/image_loader.cpp
    stitching_8k/strip_extraction.cpp
    stitching_8k/movement_detection.cpp
    stitching_8k/image_blending.cpp
    stitching_8k/panorama_processor.cpp
    stitching_8k/fs_util.cpp
    stitching_8k/frame_extraction_utils.cpp
)

# Define source files for extract_frames (from frame_extraction)
set(EXTRACT_FRAMES_SOURCES
    frame_extraction/main.cpp
    frame_extraction/ffmpeg_extractor.cpp
    frame_extraction/opencv_extractor.cpp
    frame_extraction/rotation_analysis.cpp
    stitching_4k/fs_util.cpp
    stitching_4k/frame_extraction_utils.cpp
)

# Add stitch_tire_4k executable
add_executable(stitch_tire_4k ${STITCH_TIRE_4K_SOURCES})
target_compile_definitions(stitch_tire_4k PRIVATE BUILD_4K)

# Add stitch_tire_8k executable
add_executable(stitch_tire_8k ${STITCH_TIRE_8K_SOURCES})

# Add extract_frames executable
add_executable(extract_frames ${EXTRACT_FRAMES_SOURCES})

# Include directories for stitch_tire_4k
target_include_directories(stitch_tire_4k PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/stitching_4k
)

# Include directories for stitch_tire_8k
target_include_directories(stitch_tire_8k PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/stitching_8k
)

# Include directories for extract_frames
target_include_directories(extract_frames PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/frame_extraction
    ${CMAKE_CURRENT_SOURCE_DIR}/stitching_4k
)

# Link libraries for stitch_tire_4k
target_link_libraries(stitch_tire_4k ${OpenCV_LIBS})

# Link libraries for stitch_tire_8k
target_link_libraries(stitch_tire_8k ${OpenCV_LIBS})

# Link libraries for extract_frames
target_link_libraries(extract_frames ${OpenCV_LIBS})

# Link OpenMP if found
if(OpenMP_CXX_FOUND)
    target_link_libraries(stitch_tire_4k ${OpenMP_CXX_LIBRARIES})
    target_link_libraries(stitch_tire_8k ${OpenMP_CXX_LIBRARIES})
    target_link_libraries(extract_frames ${OpenMP_CXX_LIBRARIES})
endif()

# Installation targets
install(TARGETS stitch_tire_4k stitch_tire_8k extract_frames
        RUNTIME DESTINATION bin)