"""
Cache cleanup and rebuild utility for the Tire Panorama Tool.

Provides functions to clean up __pycache__ directories and rebuild the application.
"""
import os
import sys
import shutil
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
import threading

# Import MinGW checker and DLL checker
try:
    from utils.mingw_checker import ensure_mingw_available
    from utils.dll_checker import ensure_opencv_dlls
except ImportError:
    # Handle the case when running from within the utils directory
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.mingw_checker import ensure_mingw_available
    from utils.dll_checker import ensure_opencv_dlls

def find_pycache_directories(base_dir):
    """
    Find all __pycache__ directories within the base directory.

    Args:
        base_dir: Base directory to search in

    Returns:
        List of __pycache__ directory paths
    """
    pycache_dirs = []

    for root, dirs, files in os.walk(base_dir):
        if "__pycache__" in dirs:
            pycache_dirs.append(os.path.join(root, "__pycache__"))

    return pycache_dirs

def find_build_directories(base_dir):
    """
    Find all build directories within the base directory.

    Args:
        base_dir: Base directory to search in

    Returns:
        List of build directory paths
    """
    build_dirs = []

    for root, dirs, files in os.walk(base_dir):
        if "build" in dirs and os.path.basename(root) != "CMakeLists.txt":
            # Check if it's a CMake build directory
            build_path = os.path.join(root, "build")
            if os.path.exists(os.path.join(build_path, "CMakeCache.txt")):
                build_dirs.append(build_path)

    return build_dirs

def delete_directories(dirs):
    """
    Delete a list of directories.

    Args:
        dirs: List of directory paths to delete

    Returns:
        Tuple of (success_count, failed_dirs) with number of successfully deleted directories
        and list of directories that couldn't be deleted
    """
    success_count = 0
    failed_dirs = []

    for directory in dirs:
        try:
            if os.path.exists(directory):
                shutil.rmtree(directory)
                success_count += 1
        except Exception as e:
            failed_dirs.append((directory, str(e)))

    return success_count, failed_dirs

def rebuild_project(base_dir, status_callback=None):
    """
    Rebuild the C++ components of the project using MinGW.

    Args:
        base_dir: Base directory of the project
        status_callback: Optional callback function for status updates

    Returns:
        True if rebuild was successful, False otherwise
    """
    if status_callback:
        status_callback(f"rebuild_project: Attempting to rebuild C++ components in base_dir: '{base_dir}'")

    # Check if MinGW is available
    if status_callback:
        status_callback("Checking for MinGW installation...")

    mingw_success, mingw_message = ensure_mingw_available()
    if not mingw_success:
        if status_callback:
            status_callback(f"MinGW error: {mingw_message}")
        return False

    if status_callback:
        status_callback(f"MinGW check: {mingw_message}")

    # Find CMakeLists.txt directories
    cmake_dirs = []
    for root, dirs, files in os.walk(base_dir):
        if "CMakeLists.txt" in files:
            cmake_dirs.append(root)

    if status_callback:
        status_callback(f"rebuild_project: Found CMakeLists.txt in directories: {cmake_dirs}")

    if not cmake_dirs:
        if status_callback:
            status_callback(f"rebuild_project: No CMakeLists.txt found in '{base_dir}'. Skipping C++ rebuild.")
        return True # Return True as no action was taken, not strictly an error of this function

    rebuild_success = True

    for cmake_dir in cmake_dirs:
        if status_callback:
            status_callback(f"Rebuilding in {os.path.basename(cmake_dir)} (path: {cmake_dir})...")

        # Create build directory, ensuring it's clean
        build_dir = os.path.join(cmake_dir, "build")
        if status_callback:
            status_callback(f"Preparing clean build directory: {build_dir}")
        if os.path.exists(build_dir):
            try:
                shutil.rmtree(build_dir)
                if status_callback:
                    status_callback(f"Removed existing build directory: {build_dir}")
            except Exception as e:
                if status_callback:
                    status_callback(f"Error removing build directory {build_dir}: {str(e)}. Attempting to continue.")
                # Not necessarily fatal, os.makedirs might still work or handle it

        try:
            os.makedirs(build_dir, exist_ok=True)
            if status_callback:
                status_callback(f"Created fresh build directory: {build_dir}")
        except Exception as e:
            if status_callback:
                status_callback(f"Error creating build directory {build_dir}: {str(e)}. Skipping rebuild for this directory.")
            rebuild_success = False
            continue

        # Also remove any CMake cache files in the source directory to ensure clean build
        cmake_cache_files = [
            os.path.join(cmake_dir, "CMakeCache.txt"),
            os.path.join(cmake_dir, "cmake_install.cmake"),
            os.path.join(cmake_dir, "Makefile")
        ]
        for cache_file in cmake_cache_files:
            if os.path.exists(cache_file):
                try:
                    os.remove(cache_file)
                    if status_callback:
                        status_callback(f"Removed CMake cache file: {os.path.basename(cache_file)}")
                except Exception as e:
                    if status_callback:
                        status_callback(f"Warning: Could not remove {os.path.basename(cache_file)}: {str(e)}")

        # Remove CMakeFiles directory in source if it exists
        cmake_files_dir = os.path.join(cmake_dir, "CMakeFiles")
        if os.path.exists(cmake_files_dir):
            try:
                shutil.rmtree(cmake_files_dir)
                if status_callback:
                    status_callback("Removed CMakeFiles directory from source")
            except Exception as e:
                if status_callback:
                    status_callback(f"Warning: Could not remove CMakeFiles directory: {str(e)}")

        # Run CMake with MinGW generator
        if status_callback:
            status_callback(f"Running CMake with MinGW in {os.path.basename(cmake_dir)}...")

        try:
            # Change to build directory
            old_dir = os.getcwd()
            os.chdir(build_dir)

            # Run cmake command with MinGW generator and specify the compiler
            cmake_process = subprocess.run(
                ["cmake", "..", "-G", "MinGW Makefiles",
                 "-DCMAKE_CXX_COMPILER=g++",
                 "-DCMAKE_C_COMPILER=gcc"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if cmake_process.returncode != 0:
                if status_callback:
                    status_callback(f"CMake failed in {os.path.basename(cmake_dir)}: {cmake_process.stderr}")
                rebuild_success = False
                os.chdir(old_dir)
                continue

            # Build using MinGW
            if status_callback:
                status_callback(f"Building with MinGW in {os.path.basename(cmake_dir)}...")

            # Use cmake --build instead of make for better cross-platform support
            build_process = subprocess.run(
                ["cmake", "--build", "."],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if build_process.returncode != 0:
                if status_callback:
                    status_callback(f"Build failed in {os.path.basename(cmake_dir)}: {build_process.stderr}")
                rebuild_success = False
            else:
                if status_callback:
                    status_callback(f"Successfully rebuilt {os.path.basename(cmake_dir)}")

                # Corrected paths for executables.
                # cmake_dir is, e.g., "a:\\ReifenScanner\\TireStitcher"
                # Project root (ReifenScanner folder) is one level up from cmake_dir.
                project_root_dir = os.path.dirname(cmake_dir)  # Should be "a:\\ReifenScanner"

                # Executables are placed here by CMake due to CMAKE_RUNTIME_OUTPUT_DIRECTORY set in CMakeLists.txt
                stitch_tire_4k_exe_final_path = os.path.join(project_root_dir, "stitch_tire_4k.exe")
                stitch_tire_8k_exe_final_path = os.path.join(project_root_dir, "stitch_tire_8k.exe")
                extract_frames_exe_final_path = os.path.join(project_root_dir, "extract_frames.exe")

                # Verify executables exist in project_root_dir (where CMake should have placed them)
                if (os.path.exists(stitch_tire_4k_exe_final_path) and
                    os.path.exists(stitch_tire_8k_exe_final_path) and
                    os.path.exists(extract_frames_exe_final_path)):
                    if status_callback:
                        status_callback(f"Executables successfully created/updated in {project_root_dir}")

                    # Check and copy OpenCV DLLs for stitch_tire_4k.exe
                    opencv_dir = "A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/bin"  # Hardcoded path
                    if status_callback:
                        status_callback(f"Checking OpenCV DLLs for {os.path.basename(stitch_tire_4k_exe_final_path)}...")

                    dll_dir = os.path.join(project_root_dir, "dll")
                    os.makedirs(dll_dir, exist_ok=True)

                    success, copied_dlls, missing_dlls = ensure_opencv_dlls(
                        stitch_tire_4k_exe_final_path, opencv_dir, dll_dir
                    )

                    if success:
                        if copied_dlls:
                            if status_callback:
                                status_callback(f"Copied {len(copied_dlls)} DLLs to {dll_dir}")
                        else:
                            if status_callback:
                                status_callback(f"All required DLLs already present in {dll_dir}")
                    else:
                        if status_callback:
                            status_callback(f"Warning: Missing {len(missing_dlls)} DLLs for {os.path.basename(stitch_tire_4k_exe_final_path)}")
                            for dll in missing_dlls:
                                status_callback(f"  - Missing: {dll}")
                else:
                    # Executables not found where CMake should have put them.
                    if status_callback:
                        status_callback(f"Warning: Executables not found in {project_root_dir} after build attempt. Trying fallback...")

                    # Fallback logic: Try to copy from ReifenScannerMode
                    reifenscanner_mode_dir = "A:\\ReifenScannerMode"  # Hardcoded path
                    mode_stitch_tire_4k = os.path.join(reifenscanner_mode_dir, "stitch_tire_4k.exe")
                    mode_stitch_tire_8k = os.path.join(reifenscanner_mode_dir, "stitch_tire_8k.exe")
                    mode_extract_frames = os.path.join(reifenscanner_mode_dir, "extract_frames.exe")

                    # Try to copy stitch_tire_4k.exe from fallback to the correct final destination
                    if not os.path.exists(stitch_tire_4k_exe_final_path) and os.path.exists(mode_stitch_tire_4k):
                        try:
                            shutil.copy2(mode_stitch_tire_4k, stitch_tire_4k_exe_final_path)
                            if status_callback:
                                status_callback(f"Copied stitch_tire_4k.exe from {reifenscanner_mode_dir} to {project_root_dir}")
                        except Exception as e:
                            if status_callback:
                                status_callback(f"Failed to copy stitch_tire_4k.exe from fallback: {str(e)}")

                    # Try to copy stitch_tire_8k.exe from fallback to the correct final destination
                    if not os.path.exists(stitch_tire_8k_exe_final_path) and os.path.exists(mode_stitch_tire_8k):
                        try:
                            shutil.copy2(mode_stitch_tire_8k, stitch_tire_8k_exe_final_path)
                            if status_callback:
                                status_callback(f"Copied stitch_tire_8k.exe from {reifenscanner_mode_dir} to {project_root_dir}")
                        except Exception as e:
                            if status_callback:
                                status_callback(f"Failed to copy stitch_tire_8k.exe from fallback: {str(e)}")

                    # Try to copy extract_frames.exe from fallback to the correct final destination
                    if not os.path.exists(extract_frames_exe_final_path) and os.path.exists(mode_extract_frames):
                        try:
                            shutil.copy2(mode_extract_frames, extract_frames_exe_final_path)
                            if status_callback:
                                status_callback(f"Copied extract_frames.exe from {reifenscanner_mode_dir} to {project_root_dir}")
                        except Exception as e:
                            if status_callback:
                                status_callback(f"Failed to copy extract_frames.exe from fallback: {str(e)}")

                    # Final check after fallback attempt
                    if (os.path.exists(stitch_tire_4k_exe_final_path) and
                        os.path.exists(stitch_tire_8k_exe_final_path) and
                        os.path.exists(extract_frames_exe_final_path)):
                        if status_callback:
                            status_callback(f"Executables now available in {project_root_dir} (possibly from fallback).")

                        # Perform DLL checks for stitch_tire_4k.exe if it's now available
                        opencv_dir = "A:/OpenCV-MinGW-Build-OpenCV-4.5.5-x64/x64/mingw/bin"  # Hardcoded path
                        if status_callback:
                            status_callback(f"Checking OpenCV DLLs for {os.path.basename(stitch_tire_4k_exe_final_path)} (after fallback)...")

                        dll_dir = os.path.join(project_root_dir, "dll")
                        os.makedirs(dll_dir, exist_ok=True)

                        success, copied_dlls, missing_dlls = ensure_opencv_dlls(
                            stitch_tire_4k_exe_final_path, opencv_dir, dll_dir
                        )

                        if success:
                            if copied_dlls:
                                if status_callback:
                                    status_callback(f"Copied {len(copied_dlls)} DLLs to {dll_dir} (after fallback)")
                            else:
                                if status_callback:
                                    status_callback(f"All required DLLs already present in {dll_dir} (after fallback)")
                        else:
                            if status_callback:
                                status_callback(f"Warning: Missing {len(missing_dlls)} DLLs for {os.path.basename(stitch_tire_4k_exe_final_path)} (after fallback)")
                                for dll in missing_dlls:
                                    status_callback(f"  - Missing: {dll}")
                    else:
                        if status_callback:
                            status_callback(f"Warning: Executables still not found in {project_root_dir} even after fallback.")

            # Change back to original directory
            os.chdir(old_dir)

        except Exception as e:
            if status_callback:
                status_callback(f"Error rebuilding {os.path.basename(cmake_dir)}: {str(e)}")
            rebuild_success = False

            # Ensure we change back to original directory
            if os.getcwd() != old_dir:
                os.chdir(old_dir)

    return rebuild_success

def clean_and_rebuild(app, status_var=None):
    """
    Clean up cache and rebuild the project.

    Args:
        app: Application instance
        status_var: Optional StringVar for status updates

    Returns:
        None
    """
    # Use app's status var if none provided
    if status_var is None and hasattr(app, 'status_var'):
        status_var = app.status_var

    # Reset custom video folder if it exists
    if hasattr(app, 'custom_video_folder'):
        app.custom_video_folder = None
        if status_var:
            status_var.set("Reset custom video folder path")

    # Function to update status
    def update_status(message):
        if status_var:
            status_var.set(message)
        print(message)

    # Find the base directory of the project (should be TireStitcher folder)
    base_dir = None
    if hasattr(app, 'gui_dir') and app.gui_dir and os.path.isdir(app.gui_dir):
        # app.gui_dir should be '.../TireStitcher/gui'
        # So, os.path.dirname(app.gui_dir) should be '.../TireStitcher'
        candidate_base_dir = os.path.dirname(app.gui_dir)
        if os.path.basename(app.gui_dir) == 'gui': # Make sure gui_dir is actually the gui folder
            base_dir = candidate_base_dir
            update_status(f"Using os.path.dirname(app.gui_dir) for base_dir: {base_dir}")
        else:
            update_status(f"app.gui_dir ('{app.gui_dir}') does not appear to be the 'gui' directory. Fallback needed.")
    else:
        update_status(f"app.gui_dir not found or invalid. Using fallback to determine base_dir.")

    if not base_dir:
        # Fallback: __file__ is .../utils/cache_cleanup.py
        # os.path.dirname(__file__) is .../utils
        # os.path.dirname(os.path.dirname(__file__)) is .../TireStitcher
        utils_dir = os.path.dirname(os.path.abspath(__file__))
        base_dir = os.path.dirname(utils_dir)
        update_status(f"Using fallback for base_dir (parent of utils directory): {base_dir}")

    update_status(f"Final base_dir for C++ rebuild: {base_dir}")

    # Start a thread for the process
    cleanup_thread = threading.Thread(
        target=_clean_and_rebuild_thread,
        args=(base_dir, update_status),
        daemon=True
    )
    cleanup_thread.start()

def delete_executables(base_dir, update_status):
    """
    Delete existing executables in the ReifenScanner directory.

    Args:
        base_dir: Base directory of the project
        update_status: Function to call with status updates

    Returns:
        Tuple of (success_count, failed_exes) with number of successfully deleted executables
        and list of executables that couldn't be deleted
    """
    # Get the TireStitcher directory
    tire_stitcher_dir = base_dir

    # Get the ReifenScanner directory (parent of TireStitcher)
    reifen_scanner_dir = os.path.dirname(tire_stitcher_dir)

    # List of executables to delete in ReifenScanner folder
    executables = [
        os.path.join(reifen_scanner_dir, "stitch_tire.exe"),  # Old unified executable
        os.path.join(reifen_scanner_dir, "stitch_tire_4k.exe"),  # New 4K executable
        os.path.join(reifen_scanner_dir, "stitch_tire_8k.exe"),  # New 8K executable
        os.path.join(reifen_scanner_dir, "extract_frames.exe")
    ]

    # Also check for executables in TireStitcher folder (for backward compatibility)
    executables.extend([
        os.path.join(tire_stitcher_dir, "stitch_tire.exe"),  # Old unified executable
        os.path.join(tire_stitcher_dir, "stitch_tire_4k.exe"),  # New 4K executable
        os.path.join(tire_stitcher_dir, "stitch_tire_8k.exe"),  # New 8K executable
        os.path.join(tire_stitcher_dir, "extract_frames.exe")
    ])

    success_count = 0
    failed_exes = []

    for exe in executables:
        if os.path.exists(exe):
            try:
                update_status(f"Deleting executable: {os.path.basename(exe)}")
                os.remove(exe)
                success_count += 1
            except Exception as e:
                failed_exes.append((exe, str(e)))
                update_status(f"Failed to delete {os.path.basename(exe)}: {str(e)}")

    return success_count, failed_exes

def _clean_and_rebuild_thread(base_dir, update_status):
    """
    Background thread for cleanup and rebuild process.

    Args:
        base_dir: Base directory of the project
        update_status: Function to call with status updates

    Returns:
        None
    """
    try:
        update_status("Finding __pycache__ directories...")
        pycache_dirs = find_pycache_directories(base_dir)

        if pycache_dirs:
            update_status(f"Found {len(pycache_dirs)} __pycache__ directories")

            # Delete __pycache__ directories
            success_count, failed_dirs = delete_directories(pycache_dirs)

            if failed_dirs:
                update_status(f"Deleted {success_count} __pycache__ directories, {len(failed_dirs)} failed")
            else:
                update_status(f"Successfully deleted {success_count} __pycache__ directories")
        else:
            update_status("No __pycache__ directories found")

        # Find and delete build directories
        update_status("Finding build directories...")
        build_dirs = find_build_directories(base_dir)

        if build_dirs:
            update_status(f"Found {len(build_dirs)} build directories")

            # Delete build directories
            success_count, failed_dirs = delete_directories(build_dirs)

            if failed_dirs:
                update_status(f"Deleted {success_count} build directories, {len(failed_dirs)} failed")
            else:
                update_status(f"Successfully deleted {success_count} build directories")
        else:
            update_status("No build directories found")

        # Delete existing executables
        update_status("Deleting existing executables...")
        success_count, failed_exes = delete_executables(base_dir, update_status)

        if success_count > 0:
            update_status(f"Successfully deleted {success_count} executables")

        # Rebuild project
        update_status("Starting rebuild process...")

        if rebuild_project(base_dir, update_status):
            update_status("Rebuild completed successfully")
        else:
            update_status("Rebuild completed with errors. See output for details.")

    except Exception as e:
        update_status(f"Error during cleanup and rebuild: {str(e)}")

def add_rebuild_button(app, parent_widget):
    """
    Add a rebuild button to the specified parent widget.

    Args:
        app: Application instance
        parent_widget: Parent widget to add the button to

    Returns:
        The created button
    """
    # Create button with Unicode symbol
    rebuild_btn = ttk.Button(
        parent_widget,
        text="Rebuild",
        command=lambda: clean_and_rebuild(app)
    )

    # Add tooltip
    _create_tooltip(rebuild_btn, "Clean cache and rebuild the project")

    return rebuild_btn

# Simple tooltip implementation
class ToolTip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tipwindow = None
        self.id = None
        self.x = self.y = 0
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)

    def enter(self, event=None):
        self.schedule()

    def leave(self, event=None):
        self.unschedule()
        self.hidetip()

    def schedule(self):
        self.unschedule()
        self.id = self.widget.after(500, self.showtip)

    def unschedule(self):
        id = self.id
        self.id = None
        if id:
            self.widget.after_cancel(id)

    def showtip(self):
        x = self.widget.winfo_rootx() + self.widget.winfo_width() // 2
        y = self.widget.winfo_rooty() + self.widget.winfo_height()

        self.tipwindow = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(1)
        tw.wm_geometry(f"+{x}+{y}")

        label = ttk.Label(tw, text=self.text, background="#ffffe0", relief="solid", borderwidth=1)
        label.pack()

    def hidetip(self):
        tw = self.tipwindow
        self.tipwindow = None
        if tw:
            tw.destroy()

def _create_tooltip(widget, text):
    """Create a tooltip for a widget"""
    return ToolTip(widget, text)