#include "rotation_analysis.h"
#include "../stitching_4k/frame_extraction_utils.h"
#include <iostream>
#include <chrono>

bool needsVerticalRotation(const cv::Mat& frame) {
    if (frame.empty()) {
        return false;
    }

    // Convert to grayscale for analysis
    cv::<PERSON> gray;
    cv::cvtColor(frame, gray, cv::COLOR_BGR2GRAY);

    // Resize for faster processing
    cv::Mat smallGray;
    cv::resize(gray, smallGray, cv::Size(), 0.25, 0.25);

    // Apply Sobel filter to detect edges in X and Y direction
    cv::Mat gradX, gradY;
    cv::Sobel(smallGray, gradX, CV_16S, 1, 0);
    cv::Sobel(smallGray, gradY, CV_16S, 0, 1);

    // Convert to absolute values
    cv::convertScaleAbs(gradX, gradX);
    cv::convertScaleAbs(gradY, gradY);

    // Calculate sum of gradients in each direction
    double sumX = cv::sum(gradX)[0];
    double sumY = cv::sum(gradY)[0];

    // Calculate the gradient ratio
    double ratio = sumX / (sumY + 1e-5); // Avoid division by zero

    std::cout << "DEBUG: Edge gradient ratio (X/Y): " << ratio
              << " - " << (ratio > 1.2 ? "Vertical rotation needed" : "No vertical rotation needed") << std::endl;

    // If more horizontal lines than vertical, then we need to rotate
    // This threshold (1.2) can be adjusted based on testing
    return ratio > 1.2;
}

bool needsHorizontalFlip(const cv::Mat& frame1, const cv::Mat& frame2) {
    // Both frames should be already vertically rotated if needed
    if (frame1.empty() || frame2.empty()) {
        return false;
    }

    // Resize for faster processing
    cv::Mat small1, small2;
    cv::resize(frame1, small1, cv::Size(), 0.25, 0.25);
    cv::resize(frame2, small2, cv::Size(), 0.25, 0.25);

    // Convert to grayscale
    cv::Mat gray1, gray2;
    cv::cvtColor(small1, gray1, cv::COLOR_BGR2GRAY);
    cv::cvtColor(small2, gray2, cv::COLOR_BGR2GRAY);

    // Calculate optical flow using template matching
    int width = gray1.cols;
    int height = gray1.rows;

    // Take a strip from the middle of the first image
    int stripWidth = width / 5;
    int stripStart = (width - stripWidth) / 2;
    cv::Mat templ = gray1(cv::Rect(stripStart, 0, stripWidth, height));

    // Define a wider search area in the second image
    int searchWidth = stripWidth * 3;
    int searchStart = std::max(0, stripStart - stripWidth);
    int searchEnd = std::min(width, stripStart + stripWidth * 2);

    if (searchEnd - searchStart <= stripWidth) {
        return false; // Can't determine direction
    }

    cv::Mat searchArea = gray2(cv::Rect(searchStart, 0, searchEnd - searchStart, height));

    // Template matching
    cv::Mat result;
    cv::matchTemplate(searchArea, templ, result, cv::TM_CCOEFF_NORMED);

    double maxVal;
    cv::Point maxLoc;
    cv::minMaxLoc(result, nullptr, &maxVal, nullptr, &maxLoc);

    // Calculate flow direction
    int matchPosInSearchArea = maxLoc.x + stripWidth/2;
    int expectedPosInSearchArea = (searchEnd - searchStart) / 2;

    // Negative value means flow is to the left
    double movement = matchPosInSearchArea - expectedPosInSearchArea;

    std::cout << "DEBUG: Flow direction measurement: " << movement
              << " - " << (movement > 0 ? "Negative (needs 180° flip)" : "Positive (no flip needed)") << std::endl;

    // If movement is positive, we need to flip horizontally (180 degrees)
    return movement > 0;
}

std::pair<bool, bool> quickRotationAnalysis(const std::string& videoPath) {
    PerformanceTimer timer("Rotation analysis");
    std::cout << "Analyzing rotation needs (quick method)..." << std::endl;

    cv::VideoCapture cap(videoPath);
    if (!cap.isOpened()) {
        std::cerr << "Error: Could not open video for rotation detection: " << videoPath << std::endl;
        return {false, false}; // Default to no rotation
    }

    // Get only the first two frames
    cv::Mat firstFrame, secondFrame;

    if (!cap.read(firstFrame) || !cap.read(secondFrame)) {
        cap.release();
        return {false, false};
    }

    // Check vertical rotation need
    bool needsVerticalRotate = needsVerticalRotation(firstFrame);

    // Apply vertical rotation if needed for the flow check
    cv::Mat rotatedFirst = firstFrame;
    cv::Mat rotatedSecond = secondFrame;

    if (needsVerticalRotate) {
        cv::rotate(firstFrame, rotatedFirst, cv::ROTATE_90_CLOCKWISE);
        cv::rotate(secondFrame, rotatedSecond, cv::ROTATE_90_CLOCKWISE);
    }

    // Check flow direction
    bool needsHorizontalRotate = needsHorizontalFlip(rotatedFirst, rotatedSecond);

    cap.release();

    std::cout << "Rotation analysis results: "
              << "Vertical=" << (needsVerticalRotate ? "Yes" : "No")
              << ", Horizontal=" << (needsHorizontalRotate ? "Yes" : "No") << std::endl;

    return {needsVerticalRotate, needsHorizontalRotate};
}