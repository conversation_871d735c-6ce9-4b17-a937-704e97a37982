#include "../stitching_4k/frame_extraction_utils.h"
#include "ffmpeg_extractor.h"
#include "opencv_extractor.h"
#include <iostream>
#include <string>

// Define DEBUG_PATH_LOGGING if not already defined by build system
// #define DEBUG_PATH_LOGGING

// Main function to extract frames
bool extractFrames(const std::string& videoPath, const std::string& outputFolder, const ExtractOptions& options) {
#ifdef DEBUG_PATH_LOGGING
    std::cout << "DEBUG_PATH_LOGGING: videoPath: " << videoPath << std::endl;
    std::cout << "DEBUG_PATH_LOGGING: outputFolder: " << outputFolder << std::endl;
#endif
    // First try FFMPEG, fallback to OpenCV if not available
    if (!options.forceOpenCV && isFFMPEGAvailable()) {
        bool ffmpegSuccess = extractFramesFFMPEG(videoPath, outputFolder, options);
        if (ffmpegSuccess) {
            return true;  // FFMPEG worked successfully
        }
        std::cout << "FFMPEG extraction failed, falling back to OpenCV" << std::endl;
    } else {
        if (options.forceOpenCV) {
            std::cout << "OpenCV extraction forced by user" << std::endl;
        } else {
            std::cout << "FFMPEG not available in system PATH" << std::endl;
        }
    }

    // If we get here, use OpenCV (either forced or as fallback)
    return extractFramesOpenCV(videoPath, outputFolder, options);
}

void printUsage(const char* programName) {
    std::cerr << "Usage: " << programName << " <video_path> <output_folder> [Options]" << std::endl;
    std::cerr << "Options:" << std::endl;
    std::cerr << "  --rotate              Force rotate frames 90 degrees clockwise" << std::endl;
    std::cerr << "  --no-auto-rotate      Disable automatic rotation detection" << std::endl;
    std::cerr << "  --samples <n>         Extract only every n-th frame" << std::endl;
    std::cerr << "  --force-opencv        Force using OpenCV instead of FFMPEG" << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "Frame Extraction Tool" << std::endl;
    std::cout << "=====================" << std::endl;

    if (argc < 3) {
        printUsage(argv[0]);
        return 1;
    }

    std::string videoPath = argv[1];
    std::string outputFolder = argv[2];

#ifdef DEBUG_PATH_LOGGING
    std::cout << "DEBUG_PATH_LOGGING: Initial videoPath: " << videoPath << std::endl;
    std::cout << "DEBUG_PATH_LOGGING: Initial outputFolder: " << outputFolder << std::endl;
#endif

    // Parse options
    ExtractOptions options;

    for (int i = 3; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--rotate") {
            options.forceRotate = true;
            options.autoRotate = false;
        }
        else if (arg == "--max-frames" && i + 1 < argc) {
            options.maxFrames = std::stoi(argv[++i]);
            if (options.maxFrames < 1) options.maxFrames = 0; // 0 means unlimited
        }
        else if (arg == "--no-auto-rotate") {
            options.autoRotate = false;
        }
        else if (arg == "--samples" && i + 1 < argc) {
            options.sampleRate = std::stoi(argv[++i]);
            if (options.sampleRate < 1) options.sampleRate = 1;
        }
        else if (arg == "--force-opencv") {
            options.forceOpenCV = true;
        }
        else if (arg == "--help") {
            printUsage(argv[0]);
            return 0;
        }
    }

    // Extract frames (will use FFMPEG with fallback to OpenCV)
    bool success = extractFrames(videoPath, outputFolder, options);

    return success ? 0 : 1;
}