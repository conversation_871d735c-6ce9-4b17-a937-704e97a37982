#ifndef FRAME_EXTRACTION_OPENCV_EXTRACTOR_H
#define FRAME_EXTRACTION_OPENCV_EXTRACTOR_H

#include "../stitching_4k/frame_extraction_utils.h"
#include <string>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat

// Extract frames using OpenCV (fallback method)
// Returns true if successful, false otherwise
bool extractFramesOpenCV(const std::string& videoPath,
                         const std::string& outputFolder,
                         const ExtractOptions& options);

#endif // FRAME_EXTRACTION_OPENCV_EXTRACTOR_H