#ifndef FRAME_EXTRACTION_FFMPEG_EXTRACTOR_H
#define FRAME_EXTRACTION_FFMPEG_EXTRACTOR_H

#include "../stitching_4k/frame_extraction_utils.h"
#include <string>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat
#ifdef _WIN32
#include <windows.h>
#endif

// Check if ffmpeg is available in system PATH
bool isFFMPEGAvailable();

// Function to extract frames using FFMPEG (shell command)
// Returns true if successful, false otherwise
bool extractFramesFFMPEG(const std::string& videoPath,
                         const std::string& outputFolder,
                         const ExtractOptions& options);

#endif // FRAME_EXTRACTION_FFMPEG_EXTRACTOR_H