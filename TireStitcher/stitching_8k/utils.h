#ifndef STITCH_TIRE_UTILS_H
#define STITCH_TIRE_UTILS_H

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

// Get CPU info for performance optimization
int getOptimalThreadCount();

// Parse a comma-separated list of frame numbers
std::vector<int> parseFrameList(const std::string& frameList);

// Format time duration into a readable string
std::string formatDuration(double seconds);

// Validate output directory and create if it doesn't exist
bool validateOutputDirectory(const std::string& outputDir);

// Check if an image is valid
bool isImageValid(const cv::Mat& image);

// Get file extension in lowercase
std::string getFileExtension(const std::string& filename);

#endif // STITCH_TIRE_UTILS_H
