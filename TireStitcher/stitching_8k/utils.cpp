#include "config.h"
#include "utils.h"
#include "strip_extraction.h"
#include "image_loader.h"
#include "movement_detection.h"
#include "image_blending.h"
#include "panorama_processor.h"

#include <opencv2/opencv.hpp>
#include <iostream>
#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <algorithm>
#include <sstream>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat

// Use the fs_util namespace from main.cpp
namespace fs_util {
    bool exists(const std::string& path);
    bool create_directory(const std::string& path);
    bool create_directories(const std::string& path);
    std::string filename(const std::string& path);
    std::string extension(const std::string& path);
}

// Reference the mutex defined in main.cpp instead of redefining it
extern std::mutex coutMutex;

// Additional utility functions can be placed here

// Get CPU info for performance optimization
int getOptimalThreadCount() {
    int cpuThreads = std::thread::hardware_concurrency();

    // Use 75% of available threads for processing
    int optimalThreads = std::max(1, static_cast<int>(cpuThreads * 0.75));

    safePrint("CPU has " + std::to_string(cpuThreads) +
              " threads, using " + std::to_string(optimalThreads) +
              " threads for processing", true);

    return optimalThreads;
}

// Parse a comma-separated list of frame numbers
std::vector<int> parseFrameList(const std::string& frameList) {
    std::vector<int> result;
    std::stringstream ss(frameList);
    std::string item;

    while (std::getline(ss, item, ',')) {
        try {
            result.push_back(std::stoi(item));
        }
        catch (const std::exception& e) {
            safePrint("Warning: Invalid frame number in list: " + item, true);
        }
    }

    return result;
}

// Format time duration into a readable string
std::string formatDuration(double seconds) {
    int minutes = static_cast<int>(seconds) / 60;
    int remainingSeconds = static_cast<int>(seconds) % 60;

    if (minutes > 0) {
        return std::to_string(minutes) + "m " + std::to_string(remainingSeconds) + "s";
    } else {
        return std::to_string(remainingSeconds) + "s";
    }
}

// Validate output directory and create if it doesn't exist
bool validateOutputDirectory(const std::string& outputDir) {
    try {
        if (!fs_util::exists(outputDir)) {
            fs_util::create_directories(outputDir);
            safePrint("Created output directory: " + outputDir, true);
        }
        return true;
    } catch (const std::exception& e) {
        safePrint("Error creating output directory: " + std::string(e.what()), true);
        return false;
    }
}

// Check if an image is valid
bool isImageValid(const cv::Mat& image) {
    return !image.empty() && image.cols > 0 && image.rows > 0;
}

// Get file extension in lowercase
std::string getFileExtension(const std::string& filename) {
    std::string ext = fs_util::extension(filename);
    std::transform(ext.begin(), ext.end(), ext.begin(),
                   [](unsigned char c){ return std::tolower(c); });
    return ext;
}